// Package annotation 沙箱在线操作实现
package annotation

import (
	"context"
	"encoding/json"
	"fmt"
	_ "image/jpeg" // 注册JPEG解码器
	"math/rand"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"

	"mmfinderdrsandboxagentsvr/errors"
	"mmfinderdrsandboxagentsvr/middleware/db"
	agent_service_model "mmfinderdrsandboxagentsvr/model/service/agent"
	"mmfinderdrsandboxagentsvr/service/agent"
)

func getSpecialStr1(actionInput map[string]any) string {
	jsonData, err := json.Marshal(actionInput)
	if err != nil {
		return ""
	}
	return string(jsonData)
}

// ClickAction 点击操作
func ClickAction(
	ctx context.Context,
	traceID string,
	uin string,
	appID string,
	x, y int64,
	xpath string,
) (*agent_service_model.PythonResult, error) {
	rand.New(rand.NewSource(time.Now().UnixNano()))
	if traceID == "" {
		var err error
		traceID, err = db.GetLatestSessionTraceID(uin, appID)
		if err != nil {
			return nil, errors.BadResponse.WithMessagef("获取traceID失败：error=%v", err)
		}
	}
	spanID := fmt.Sprintf("%016x", rand.Uint64())
	actionInput := map[string]any{
		"type": "click",
	}
	if xpath != "" {
		actionInput["xpath"] = xpath
	} else {
		actionInput["x"] = x
		actionInput["y"] = y
	}
	args := []string{
		"--app_id", appID,
		"--run_mode", "annotation",
		"--sandbox_close_applet", "0",
		"--sandbox_skip_launch_applet", "1",
		"--special_str_1", getSpecialStr1(actionInput),
		"--uin", uin,
		"--username", "wxid_7tzy6unka3lm12",
		"--headless_mode", "2",
		"--from_username", "wxid_7tzy6unka3lm12",
		"--traceparent", fmt.Sprintf("00-%s-%s-01", traceID, spanID),
		"--tracestate", "",
	}
	inferResult, err := agent.RunAgentInferPython(ctx, "", nil, true, args, []string{})
	if err != nil {
		errMsg := "点击操作失败"
		log.WithContext(
			ctx,
			log.Field{Key: "error", Value: err},
		).Error(errMsg)
		return nil, errors.BadResponse.WithMessagef(
			"点击操作失败：error=%v", err,
		)
	}
	// 正常处理
	log.WithContext(
		ctx,
	).Info("点击操作成功")
	return inferResult.PythonResult, nil
}

// WaitAction 等待操作
func WaitAction(ctx context.Context, traceID string, uin string, appID string) (
	*agent_service_model.PythonResult, error,
) {
	rand.New(rand.NewSource(time.Now().UnixNano()))
	if traceID == "" {
		var err error
		traceID, err = db.GetLatestSessionTraceID(uin, appID)
		if err != nil {
			return nil, errors.BadResponse.WithMessagef("获取traceID失败：error=%v", err)
		}
	}
	spanID := fmt.Sprintf("%016x", rand.Uint64())
	actionInput := map[string]any{
		"type": "wait",
	}
	args := []string{
		"--app_id", appID,
		"--run_mode", "annotation",
		"--sandbox_close_applet", "0",
		"--sandbox_skip_launch_applet", "1",
		"--special_str_1", getSpecialStr1(actionInput),
		"--uin", uin,
		"--username", "wxid_7tzy6unka3lm12",
		"--headless_mode", "2",
		"--from_username", "wxid_7tzy6unka3lm12",
		"--traceparent", fmt.Sprintf("00-%s-%s-01", traceID, spanID),
		"--tracestate", "",
	}
	inferResult, err := agent.RunAgentInferPython(ctx, "", nil, true, args, []string{})
	if err != nil {
		errMsg := "等待操作失败"
		log.WithContext(
			ctx,
			log.Field{Key: "error", Value: err},
		).Error(errMsg)
		return nil, errors.BadResponse.WithMessagef(
			"等待操作失败：error=%v", err,
		)
	}
	log.WithContext(
		ctx,
	).Info("等待操作成功")
	return inferResult.PythonResult, nil
}

// ScrollAction 滚动操作
func ScrollAction(
	ctx context.Context,
	traceID string,
	uin string, appID string, x, y int64, deltaX, deltaY int64, xpath string,
) (*agent_service_model.PythonResult, error) {
	rand.New(rand.NewSource(time.Now().UnixNano()))
	if traceID == "" {
		var err error
		traceID, err = db.GetLatestSessionTraceID(uin, appID)
		if err != nil {
			return nil, errors.BadResponse.WithMessagef("获取traceID失败：error=%v", err)
		}
	}
	spanID := fmt.Sprintf("%016x", rand.Uint64())
	actionInput := map[string]any{
		"type":    "scroll",
		"delta_x": deltaX,
		"delta_y": deltaY,
	}
	if xpath != "" {
		actionInput["xpath"] = xpath
	} else {
		actionInput["x"] = x
		actionInput["y"] = y
	}
	args := []string{
		"--app_id", appID,
		"--run_mode", "annotation",
		"--sandbox_close_applet", "0",
		"--sandbox_skip_launch_applet", "1",
		"--special_str_1", getSpecialStr1(actionInput),
		"--uin", uin,
		"--username", "wxid_7tzy6unka3lm12",
		"--headless_mode", "2",
		"--from_username", "wxid_7tzy6unka3lm12",
		"--traceparent", fmt.Sprintf("00-%s-%s-01", traceID, spanID),
		"--tracestate", "",
	}
	inferResult, err := agent.RunAgentInferPython(ctx, "", nil, true, args, []string{})
	if err != nil {
		errMsg := "滚动操作失败"
		log.WithContext(
			ctx,
			log.Field{Key: "error", Value: err},
		).Error(errMsg)
		return nil, errors.BadResponse.WithMessagef(
			"滚动操作失败：error=%v", err,
		)
	}
	log.WithContext(
		ctx,
	).Info("滚动操作成功")
	return inferResult.PythonResult, nil
}

// SearchAction 搜索操作
func SearchAction(
	ctx context.Context,
	traceID string,
	uin string, appID string, x, y int64, xpath string, text string,
) (*agent_service_model.PythonResult, error) {
	rand.New(rand.NewSource(time.Now().UnixNano()))
	if traceID == "" {
		var err error
		traceID, err = db.GetLatestSessionTraceID(uin, appID)
		if err != nil {
			return nil, errors.BadResponse.WithMessagef("获取traceID失败：error=%v", err)
		}
	}
	spanID := fmt.Sprintf("%016x", rand.Uint64())
	actionInput := map[string]any{
		"type": "search",
		"text": text,
	}
	if xpath != "" {
		actionInput["xpath"] = xpath
	} else {
		actionInput["x"] = x
		actionInput["y"] = y
	}
	args := []string{
		"--app_id", appID,
		"--run_mode", "annotation",
		"--sandbox_close_applet", "0",
		"--sandbox_skip_launch_applet", "1",
		"--special_str_1", getSpecialStr1(actionInput),
		"--uin", uin,
		"--username", "wxid_7tzy6unka3lm12",
		"--headless_mode", "2",
		"--from_username", "wxid_7tzy6unka3lm12",
		"--traceparent", fmt.Sprintf("00-%s-%s-01", traceID, spanID),
		"--tracestate", "",
	}
	inferResult, err := agent.RunAgentInferPython(ctx, "", nil, true, args, []string{})
	if err != nil {
		errMsg := "搜索操作失败"
		log.WithContext(
			ctx,
			log.Field{Key: "error", Value: err},
		).Error(errMsg)
		return nil, errors.BadResponse.WithMessagef(
			"搜索操作失败：error=%v", err,
		)
	}
	log.WithContext(
		ctx,
	).Info("搜索操作成功")
	return inferResult.PythonResult, nil
}

// LaunchAppletAction 唤起小程序操作
func LaunchAppletAction(
	ctx context.Context,
	uin string,
	appID string,
	appEntryURL string,
) (*agent_service_model.PythonResult, string, error) {
	rand.New(rand.NewSource(time.Now().UnixNano()))
	traceID := fmt.Sprintf("%016x%016x", rand.Uint64(), rand.Uint64())
	spanID := fmt.Sprintf("%016x", rand.Uint64())
	actionInput := map[string]any{
		"type": "launch_applet",
	}
	args := []string{
		"--app_id", appID,
		"--app_entry_mode", "route",
		"--app_entry_url", appEntryURL,
		"--run_mode", "annotation",
		"--sandbox_close_applet", "0",
		"--sandbox_skip_launch_applet", "0",
		"--special_str_1", getSpecialStr1(actionInput),
		"--uin", uin,
		"--username", "wxid_7tzy6unka3lm12",
		"--headless_mode", "2",
		"--from_username", "wxid_7tzy6unka3lm12",
		"--traceparent", fmt.Sprintf("00-%s-%s-01", traceID, spanID),
		"--tracestate", "",
	}
	inferResult, err := agent.RunAgentInferPython(ctx, "", nil, true, args, []string{})
	if err != nil {
		errMsg := "唤起小程序失败"
		log.WithContext(
			ctx,
			log.Field{Key: "error", Value: err},
		).Error(errMsg)
		return nil, traceID, errors.BadResponse.WithMessagef(
			"唤起小程序失败：error=%v", err,
		)
	}
	log.WithContext(
		ctx,
	).Info("唤起小程序成功")
	return inferResult.PythonResult, traceID, nil
}

// TerminateAction 终止操作
func TerminateAction(
	ctx context.Context,
	traceID string,
	uin string,
	appID string,
) (*agent_service_model.PythonResult, error) {
	rand.New(rand.NewSource(time.Now().UnixNano()))
	if traceID == "" {
		var err error
		traceID, err = db.GetLatestSessionTraceID(uin, appID)
		if err != nil {
			return nil, errors.BadResponse.WithMessagef("获取traceID失败：error=%v", err)
		}
	}
	spanID := fmt.Sprintf("%016x", rand.Uint64())
	actionInput := map[string]any{
		"type": "terminate",
	}
	args := []string{
		"--app_id", appID,
		"--run_mode", "annotation",
		"--sandbox_close_applet", "1",
		"--sandbox_skip_launch_applet", "1",
		"--special_str_1", getSpecialStr1(actionInput),
		"--uin", uin,
		"--username", "wxid_7tzy6unka3lm12",
		"--headless_mode", "2",
		"--from_username", "wxid_7tzy6unka3lm12",
		"--traceparent", fmt.Sprintf("00-%s-%s-01", traceID, spanID),
		"--tracestate", "",
	}
	inferResult, err := agent.RunAgentInferPython(ctx, "", nil, true, args, []string{})
	if err != nil {
		errMsg := "终止操作失败"
		log.WithContext(
			ctx,
			log.Field{Key: "error", Value: err},
		).Error(errMsg)
		return nil, errors.BadResponse.WithMessagef(
			"终止操作失败：error=%v", err,
		)
	}
	log.WithContext(
		ctx,
	).Info("终止操作成功")
	return inferResult.PythonResult, nil
}
