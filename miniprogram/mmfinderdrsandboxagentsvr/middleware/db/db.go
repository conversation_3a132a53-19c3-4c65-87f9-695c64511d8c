// Package db 数据库操作
package db

import (
	"encoding/json"
	"fmt"
	"time"

	sandbox_api_model "mmfinderdrsandboxagentsvr/model/api/sandbox"
	db_model "mmfinderdrsandboxagentsvr/model/middleware/db"

	trpcgorm "git.code.oa.com/trpc-go/trpc-database/gorm"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

var postgresqlDB *gorm.DB

// Init 初始化
func Init() {
	var err error
	postgresqlDB, err = trpcgorm.NewClientProxy("trpc.postgres.mmfinderdrsandbox.db")
	if err != nil {
		panic(err)
	}
}

// InsertAndUpdateAIMiniProgramSandbox 新增或者更改
func InsertAndUpdateAIMiniProgramSandbox(id int64, username string, password string, addr string) error {
	var count int64
	if err := postgresqlDB.Model(&db_model.AIMiniProgramSandbox{}).
		Where("username = ?", username).
		Count(&count).Error; err != nil {
		return err
	}

	if count > 0 {
		// 更新现有记录
		return postgresqlDB.Model(&db_model.AIMiniProgramSandbox{}).
			Where("username = ?", username).
			Updates(map[string]interface{}{
				"id":             id,
				"username":       username,
				"password":       password,
				"addr":           addr,
				"heartbeated_at": time.Now(),
			}).Error
	}
	// 创建新记录
	return postgresqlDB.Create(&db_model.AIMiniProgramSandbox{
		ID:       id,
		Username: username,
		Password: password,
		Addr:     addr,
	}).Error
}

// UpdateAIMiniProgramSandbox 更改
func UpdateAIMiniProgramSandbox(username string, addr *string, cliHeartbeatedAt *time.Time) error {
	param := make(map[string]interface{})
	if addr != nil {
		param["addr"] = addr
	}
	if cliHeartbeatedAt != nil {
		param["cli_heartbeated_at"] = cliHeartbeatedAt
	}
	return postgresqlDB.Model(&db_model.AIMiniProgramSandbox{}).
		Where("username = ?", username).
		Updates(param).Error
}

// CreateAnnotationWithOperations 创建标注记录并关联操作记录
func CreateAnnotationWithOperations(apiAnnotation *sandbox_api_model.Annotation) error {
	// 转换提交时间为Time类型
	const layout = "2006-01-02 15:04:05"
	submitTime, err := time.Parse(layout, apiAnnotation.SubmitTime)

	if err != nil {
		return err
	}

	// 构建数据库主表模型
	dbAnnotation := &db_model.AIMiniProgramAnnotation{
		Rtx:         apiAnnotation.RTX,
		AppID:       apiAnnotation.AppID,
		TargetID:    apiAnnotation.TargetID,
		UserID:      apiAnnotation.UserID,
		Instruction: apiAnnotation.Instruction,
		SubmitTime:  submitTime,
		Source:      apiAnnotation.Source,
	}

	// 转换操作记录为数据库模型
	var dbOperations []*db_model.AIMiniProgramAnnotationOperation
	for _, op := range apiAnnotation.Operations {
		markConfigJSON, err := json.Marshal(op.MarkConfig)
		if err != nil {
			return err
		}

		dbOp := &db_model.AIMiniProgramAnnotationOperation{
			Type:             op.Type,
			Text:             op.Text,
			Direction:        op.Direction,
			Length:           op.Length,
			URL:              op.URL,
			DomXML:           op.DomXML,
			AllElementsRects: op.AllElementsRects,
			ScreenWidth:      op.ScreenWidth,
			ScreenHeight:     op.ScreenHeight,
			MarkConfig:       datatypes.JSON(markConfigJSON),
		}
		dbOperations = append(dbOperations, dbOp)
	}

	// 使用事务保存数据
	return postgresqlDB.Transaction(func(tx *gorm.DB) error {
		// 先创建主表记录
		if err := tx.Create(dbAnnotation).Error; err != nil {
			return err
		}

		// 为每个操作记录设置关联的annotation_id并创建
		for _, op := range dbOperations {
			op.AnnotationID = dbAnnotation.ID
			if err := tx.Create(op).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// SoftDeleteAnnotation 软删除标注记录
func SoftDeleteAnnotation(id int64) error {
	// 使用事务进行软删除操作
	return postgresqlDB.Transaction(func(tx *gorm.DB) error {
		// 查询记录是否存在
		var annotation db_model.AIMiniProgramAnnotation
		if err := tx.First(&annotation, id).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("标注记录不存在")
			}
			return err
		}

		// 执行软删除（更新 is_deleted 字段为 1）
		if err := tx.Model(&db_model.AIMiniProgramAnnotation{}).
			Where("id = ?", id).
			Update("is_deleted", 1).Error; err != nil {
			return err
		}

		// 如果source=1且target_id不为空 则删除另一张表AIMiniProgramAnnotationTask的id=target_id对应记录的BackfilledAnnotationID
		// if annotation.Source == 1 && annotation.TargetID != "" {
		// 	if err := tx.Model(&db_model.AIMiniProgramAnnotationTask{}).
		// 		Where("id = ?", annotation.TargetID).
		// 		Update("backfilled_annotation_id", nil).Error; err != nil {
		// 		return err
		// 	}
		// }

		return nil
	})
}

// EvalAnnotation 将标注数据置为评估集
func EvalAnnotation(id int64) error {
	return postgresqlDB.Transaction(func(tx *gorm.DB) error {
		// 查询记录是否存在
		var annotation db_model.AIMiniProgramAnnotation
		if err := tx.First(&annotation, id).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("标注记录不存在")
			}
			return err
		}

		// 执行（更新 is_eval 字段为 1）
		if err := tx.Model(&db_model.AIMiniProgramAnnotation{}).
			Where("id = ?", id).
			Update("is_eval", 1).Error; err != nil {
			return err
		}
		return nil
	})
}

// SoftDeleteAnnotationOperation 软删除标注操作记录
func SoftDeleteAnnotationOperation(annotationID int64, operationID int64) error {
	// 使用事务进行软删除操作
	return postgresqlDB.Transaction(func(tx *gorm.DB) error {
		// 查询记录是否存在，同时验证annotationID和operationID的关联
		var operation db_model.AIMiniProgramAnnotationOperation
		if err := tx.Where("id = ? AND annotation_id = ?", operationID, annotationID).First(&operation).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("标注操作记录不存在或不属于指定的标注记录")
			}
			return err
		}

		// 执行软删除（更新 is_deleted 字段为 1）
		if err := tx.Model(&db_model.AIMiniProgramAnnotationOperation{}).
			Where("id = ? AND annotation_id = ?", operationID, annotationID).
			Update("is_deleted", 1).Error; err != nil {
			return err
		}

		return nil
	})
}

// UpdateAnnotationOperation 更新标注操作记录
func UpdateAnnotationOperation(annotationID int64, operationID int64,
	operationType string, markConfig *sandbox_api_model.MarkConfig, text string, length int, direction string) error {
	// 使用事务进行更新操作
	return postgresqlDB.Transaction(func(tx *gorm.DB) error {
		// 查询记录是否存在，同时验证annotationID和operationID的关联
		var operation db_model.AIMiniProgramAnnotationOperation
		if err := tx.Where("id = ? AND annotation_id = ?", operationID, annotationID).First(&operation).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("标注操作记录不存在或不属于指定的标注记录")
			}
			return err
		}

		// 准备更新的字段
		updateFields := make(map[string]interface{})
		if operationType != "" {
			updateFields["type"] = operationType
		}

		if text != "" {
			updateFields["text"] = text
		}

		if length != 0 {
			updateFields["length"] = length
		}

		if direction != "" {
			updateFields["direction"] = direction
		}

		// 处理 markConfig 字段
		if markConfig != nil {
			markConfigJSON, err := json.Marshal(markConfig)
			if err != nil {
				return fmt.Errorf("序列化 markConfig 失败: %v", err)
			}
			updateFields["mark_config"] = datatypes.JSON(markConfigJSON)
		}

		// 执行更新操作
		if len(updateFields) > 0 {
			if err := tx.Model(&db_model.AIMiniProgramAnnotationOperation{}).
				Where("id = ? AND annotation_id = ?", operationID, annotationID).
				Updates(updateFields).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// parseMarkConfig 解析MarkConfig JSON数据
func parseMarkConfig(markConfigJSON datatypes.JSON) (*sandbox_api_model.MarkConfig, error) {
	if len(markConfigJSON) == 0 {
		return nil, nil
	}

	var markConfig map[string]interface{}
	var markConfigStruct sandbox_api_model.MarkConfig

	// 先解析为map
	if err := json.Unmarshal(markConfigJSON, &markConfig); err != nil {
		return nil, err
	}

	// 将map转换为结构体
	configBytes, err := json.Marshal(markConfig)
	if err != nil {
		return nil, err
	}

	if err := json.Unmarshal(configBytes, &markConfigStruct); err != nil {
		return nil, err
	}

	return &markConfigStruct, nil
}

// convertDBAnnotationsToAPIModel 将数据库模型转换为API模型
func convertDBAnnotationsToAPIModel(
	annotations []db_model.AIMiniProgramAnnotation,
	operationsMap map[int64][]db_model.AIMiniProgramAnnotationOperation,
) ([]sandbox_api_model.Annotation, error) {
	var result []sandbox_api_model.Annotation

	for _, ann := range annotations {
		apiAnn := sandbox_api_model.Annotation{
			ID:          ann.ID,
			RTX:         ann.Rtx,
			AppID:       ann.AppID,
			IsDeleted:   ann.IsDeleted,
			IsEval:      ann.IsEval,
			IsReady:     ann.IsReady,
			Source:      ann.Source,
			TargetID:    ann.TargetID,
			UserID:      ann.UserID,
			Instruction: ann.Instruction,
			Note:        ann.Note,
			SubmitTime:  ann.SubmitTime.Format("2006-01-02 15:04:05"),
		}

		// 转换操作记录
		if ops, ok := operationsMap[ann.ID]; ok {
			for _, op := range ops {
				markConfigStruct, err := parseMarkConfig(op.MarkConfig)
				if err != nil {
					return nil, err
				}

				// 解析 ExtraInfo
				var extraInfo map[string]interface{}
				if len(op.ExtraInfo) > 0 {
					if err := json.Unmarshal(op.ExtraInfo, &extraInfo); err != nil {
						return nil, err
					}
				}

				apiAnn.Operations = append(apiAnn.Operations, sandbox_api_model.Operation{
					ID:               op.ID,
					IsDeleted:        op.IsDeleted,
					Type:             op.Type,
					Text:             op.Text,
					Direction:        op.Direction,
					Length:           op.Length,
					URL:              op.URL,
					DomXML:           op.DomXML,
					AllElementsRects: op.AllElementsRects,
					ScreenWidth:      op.ScreenWidth,
					ScreenHeight:     op.ScreenHeight,
					MarkConfig:       markConfigStruct,
					ExtraInfo:        extraInfo,
				})
			}
		}

		result = append(result, apiAnn)
	}

	return result, nil
}

// fetchOperationsMap 获取操作记录映射表
func fetchOperationsMap(annotationIDs []int64) (map[int64][]db_model.AIMiniProgramAnnotationOperation, error) {
	// 批量查询所有关联操作记录
	var dbOperations []db_model.AIMiniProgramAnnotationOperation
	if len(annotationIDs) > 0 {
		if err := postgresqlDB.Where("annotation_id IN ?", annotationIDs).Where("is_deleted = ?", 0).Order("id ASC").
			Find(&dbOperations).Error; err != nil {
			return nil, err
		}
	}

	// 构建操作记录映射表
	operationsMap := make(map[int64][]db_model.AIMiniProgramAnnotationOperation)
	for _, op := range dbOperations {
		operationsMap[op.AnnotationID] = append(operationsMap[op.AnnotationID], op)
	}

	return operationsMap, nil
}

// ListPagedAnnotationsWithOperations 获取分页的标注记录列表，包含操作记录
func ListPagedAnnotationsWithOperations(
	rtxValues []string, appID, targetID, instruction string,
	limit, offset int, isDeleted *int8, isEval *int8, source *int8, isReady *int8,
	startTime, endTime *time.Time,
) (int, []sandbox_api_model.Annotation, error) {
	var total int64
	var annotations []db_model.AIMiniProgramAnnotation

	// 构建基础查询
	query := postgresqlDB.Model(&db_model.AIMiniProgramAnnotation{})
	if len(rtxValues) > 0 {
		query = query.Where("rtx IN ?", rtxValues)
	}
	if appID != "" {
		query = query.Where("app_id = ?", appID)
	}
	if targetID != "" {
		query = query.Where("target_id = ?", targetID)
	}
	if instruction != "" {
		query = query.Where("instruction = ?", instruction)
	}
	// 添加 isDeleted 筛选条件，如果传入了isDeleted参数
	if isDeleted != nil {
		query = query.Where("is_deleted = ?", *isDeleted)
	}
	if isEval != nil {
		query = query.Where("is_eval = ?", *isEval)
	}
	// 添加 source 筛选条件，如果传入了source参数
	if source != nil {
		query = query.Where("source = ?", *source)
	}
	// 添加 isReady 筛选条件，如果传入了isReady参数
	if isReady != nil {
		query = query.Where("is_ready = ?", *isReady)
	}

	// 添加提交时间范围筛选
	if startTime != nil {
		query = query.Where("submitted_at >= ?", startTime)
	}
	if endTime != nil {
		query = query.Where("submitted_at <= ?", endTime)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return 0, nil, err
	}

	// 获取分页数据
	if err := query.
		Order("submitted_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&annotations).Error; err != nil {
		return 0, nil, err
	}

	// 收集所有annotationID用于批量查询操作记录
	annotationIDs := make([]int64, 0, len(annotations))
	for _, ann := range annotations {
		annotationIDs = append(annotationIDs, ann.ID)
	}

	// 获取操作记录映射表
	operationsMap, err := fetchOperationsMap(annotationIDs)
	if err != nil {
		return 0, nil, err
	}

	// 转换为API模型
	result, err := convertDBAnnotationsToAPIModel(annotations, operationsMap)
	if err != nil {
		return 0, nil, err
	}

	return int(total), result, nil
}

// UpdateAnnotation 更新标注记录
func UpdateAnnotation(id int64, note string) error {
	// 使用事务进行更新操作
	return postgresqlDB.Transaction(func(tx *gorm.DB) error {
		// 查询记录是否存在
		var annotation db_model.AIMiniProgramAnnotation
		if err := tx.First(&annotation, id).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("标注记录不存在")
			}
			return err
		}

		// 执行更新操作
		if err := tx.Model(&db_model.AIMiniProgramAnnotation{}).
			Where("id = ?", id).
			Update("note", note).Error; err != nil {
			return err
		}

		return nil
	})
}

// ReadyAnnotation 设置标注为ready
func ReadyAnnotation(id int64) error {
	return postgresqlDB.Transaction(func(tx *gorm.DB) error {
		// 查询记录是否存在
		var annotation db_model.AIMiniProgramAnnotation
		if err := tx.First(&annotation, id).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("标注记录不存在")
			}
			return err
		}

		// 执行（更新 is_ready 字段为 1）
		if err := tx.Model(&db_model.AIMiniProgramAnnotation{}).
			Where("id = ?", id).
			Update("is_ready", 1).Error; err != nil {
			return err
		}
		return nil
	})
}

// CreateAnnotationSession 创建 annotation_session 记录
func CreateAnnotationSession(traceID, targetID, appID, userID, rtx string) error {
	session := &db_model.AIMiniProgramAnnotationSession{
		TraceID:  traceID,
		TargetID: targetID,
		AppID:    appID,
		UserID:   userID,
		RTX:      rtx,
	}
	return postgresqlDB.Create(session).Error
}

// GetLatestSessionTraceID 获取指定 userID, appID 的最新 traceID
func GetLatestSessionTraceID(userID, appID string) (string, error) {
	var session db_model.AIMiniProgramAnnotationSession
	err := postgresqlDB.Where("user_id = ?", userID).Where("app_id = ?", appID).Order("id DESC").First(&session).Error
	if err != nil {
		return "", err
	}
	return session.TraceID, nil
}
