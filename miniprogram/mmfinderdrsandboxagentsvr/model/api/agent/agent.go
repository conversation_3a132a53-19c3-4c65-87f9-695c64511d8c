// Package agent 定义相关的接口类型
package agent

import (
	"fmt"
	"math/rand"
	"net/http"
	"strings"
	"time"
)

// PositionInfo 定义相关的接口类型
type PositionInfo struct {
	Latitude  float64 `json:"latitude"`  // 纬度，默认值 -1 表示无效值
	Longitude float64 `json:"longitude"` // 经度，默认指 -1 表示无效值
}

// BaseReq 定义相关的接口类型
type BaseReq struct {
	AppEntryMode               string `json:"app_entry_mode"`
	AppEntryURL                string `json:"app_entry_url"`
	AppID                      string `json:"app_id"`
	AppName                    string `json:"app_name"`
	BaseURL                    string `json:"base_url"`
	FromUsername               string `json:"from_username"`
	HeadlessMode               string `json:"headless_mode"`
	Instruction                string `json:"instruction"`
	ModelName                  string `json:"model_name"`
	Prompt                     string `json:"prompt"`
	PromptVLT                  string `json:"prompt_vlt"`
	PromptVLTV1                string `json:"prompt_vlt_v1"`
	RagBaseURL                 string `json:"rag_base_url"`
	RawQuery                   string `json:"raw_query"`
	RunMode                    string `json:"run_mode"` // 脚本的运行模式
	SandboxCloseApplet         string `json:"sandbox_close_applet"`
	SandboxIsAsyncActionResult string `json:"sandbox_is_async_action_result"`
	SandboxSkipLaunchApplet    string `json:"sandbox_skip_launch_applet"`
	SpecialAppIDList           string `json:"special_app_id_list"`
	SpecialStr1                string `json:"special_str_1"`
	SpecialStr2                string `json:"special_str_2"`
	UIN                        string `json:"uin"`
	Username                   string `json:"username"`
	VLTBaseURL                 string `json:"vlt_base_url"`
	VLTBaseURLV1               string `json:"vlt_base_url_v1"`
	VLTModelName               string `json:"vlt_model_name"`
	VLTModelNameV1             string `json:"vlt_model_name_v1"`
}

// Args 获取执行脚本参数与环境变量
func (req *BaseReq) Args(r *http.Request) ([]string, []string) { //nolint:gocyclo,funlen
	rand.New(rand.NewSource(time.Now().UnixNano()))
	// 如果没有 workflowRunID 则使用简化的随机生成，不保证全局唯一性
	traceID := fmt.Sprintf("%016x%016x", rand.Uint64(), rand.Uint64())
	workflowRunID := r.Header.Get("x-workflow-run-id")
	workflowRunID = strings.ReplaceAll(workflowRunID, "-", "")
	if len(workflowRunID) == 32 {
		traceID = workflowRunID
	}
	spanID := fmt.Sprintf("%016x", rand.Uint64())
	args := []string{
		"--app_id", req.AppID,
		"--traceparent", fmt.Sprintf("00-%s-%s-01", traceID, spanID),
		"--tracestate", "",
	}
	if req.AppEntryMode != "" {
		args = append(args, "--app_entry_mode", req.AppEntryMode)
	}
	if req.AppEntryURL != "" {
		args = append(args, "--app_entry_url", req.AppEntryURL)
	}
	if req.AppName != "" {
		args = append(args, "--app_name", req.AppName)
	}
	if req.BaseURL != "" {
		args = append(args, "--base_url", req.BaseURL)
	}
	if req.FromUsername != "" {
		args = append(args, "--from_username", req.FromUsername)
	}
	if req.HeadlessMode != "" {
		args = append(args, "--headless_mode", req.HeadlessMode)
	}
	if req.Instruction != "" {
		args = append(args, "--instruction", req.Instruction)
	}
	if req.ModelName != "" {
		args = append(args, "--model_name", req.ModelName)
	}
	if req.Prompt != "" {
		args = append(args, "--prompt", req.Prompt)
	}
	if req.PromptVLT != "" {
		args = append(args, "--prompt_vlt", req.PromptVLT)
	}
	if req.PromptVLTV1 != "" {
		args = append(args, "--prompt_vlt_v1", req.PromptVLTV1)
	}
	if req.RagBaseURL != "" {
		args = append(args, "--rag_base_url", req.RagBaseURL)
	}
	if req.RawQuery != "" {
		args = append(args, "--raw_query", req.RawQuery)
	}
	if req.RunMode != "" {
		args = append(args, "--run_mode", req.RunMode)
	}
	if req.SandboxCloseApplet != "" {
		args = append(args, "--sandbox_close_applet", req.SandboxCloseApplet)
	}
	if req.SandboxIsAsyncActionResult != "" {
		args = append(args, "--sandbox_is_async_action_result", req.SandboxIsAsyncActionResult)
	}
	if req.SandboxSkipLaunchApplet != "" {
		args = append(args, "--sandbox_skip_launch_applet", req.SandboxSkipLaunchApplet)
	}
	if req.SpecialAppIDList != "" {
		args = append(args, "--special_app_id_list", req.SpecialAppIDList)
	}
	if req.SpecialStr1 != "" {
		args = append(args, "--special_str_1", req.SpecialStr1)
	}
	if req.SpecialStr2 != "" {
		args = append(args, "--special_str_2", req.SpecialStr2)
	}
	if req.UIN != "" {
		args = append(args, "--uin", req.UIN)
	}
	if req.Username != "" {
		args = append(args, "--username", req.Username)
	}
	if req.VLTBaseURL != "" {
		args = append(args, "--vlt_base_url", req.VLTBaseURL)
	}
	if req.VLTBaseURLV1 != "" {
		args = append(args, "--vlt_base_url_v1", req.VLTBaseURLV1)
	}
	if req.VLTModelName != "" {
		args = append(args, "--vlt_model_name", req.VLTModelName)
	}
	if req.VLTModelNameV1 != "" {
		args = append(args, "--vlt_model_name_v1", req.VLTModelNameV1)
	}
	return args, []string{}
}

// RunReq 定义相关的接口类型
type RunReq struct {
	BaseReq
	AuthCode         string        `json:"auth_code"`
	Position         *PositionInfo `json:"position"`
	SkipShareURLData bool          `json:"skip_share_url_data"`
}

// BaseRespData 定义相关的接口类型
type BaseRespData struct {
	DebugStr          string   `json:"debug_str"`
	LongImgURL        string   `json:"long_img_url"`
	LongUniqueImgURL  string   `json:"long_unique_img_url"`
	ScreenshotImgURLs []string `json:"screenshot_img_urls"`
	ShareURL          string   `json:"share_url"`
	Thinking          []string `json:"thinking"`
}
