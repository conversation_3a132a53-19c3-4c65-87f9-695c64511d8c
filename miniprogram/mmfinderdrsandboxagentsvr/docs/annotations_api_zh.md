# 沙箱标注API文档

## 接口规范

所有接口的返回格式如下：

### 正常返回
```json
{
  "retcode": 200,
  "msg": "success",
  "data": {}  // 具体数据结构根据接口不同而不同
}
```

### 异常返回
```json
{
  "retcode": 500,
  "msg": "错误原因",
  "data": {}
}
```

## 接口列表

### 1. 启动沙箱接口

- **URL**: `http://mmfinderdrsandboxagentsvr.production.polaris:80/v1/sandbox/annotations/create`
- **方法**: `POST`
- **请求参数**:

```json
{
   "rtx": "dreamshan",  // 小程序启动的人
   "appId": "",         // 小程序id
   "userId": ""         // 账户id
}
```

- **响应参数**:

```json
{
  "data": {
    "url": "http://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/35b76134bca77625a51de6c3ccd69aa8.webp",  // 类似这样可以内网直接访问的绝对cos链接
    "domXML": "xxx",    // 页面的dom xml信息
    "screenWidth": 398, // 图片的真实宽高，我理解真实接口返回应该和我这个值差不多
    "screenHeight": 772,
    "targetId": "12121212-12121" // 沙箱的唯一id
  },
  "retcode": 200,
  "msg": "success"
}
```

### 2. 沙箱交互步骤接口

- **URL**: `http://mmfinderdrsandboxagentsvr.production.polaris:80/v1/sandbox/annotations/action`
- **方法**: `POST`
- **请求参数**:

```json
{
   "rtx": "dreamshan",       // 操作人
   "appId": "",              // 小程序id
   "userId": "",             // 账户id
   "targetId": "沙箱唯一的id",  // 沙箱唯一id
   "type": "click",          // click、search、scroll
   "markConfig": {           // 这个字段只有click和search的时候才会传（这些内容回显的时候需要，在列表接口中需要全部透传）
        "uuid": "56f75655-76cb-43d5-3859-d6e9eaa65ecb-1747191890406",
        "x": 0.185075,
        "y": 0.244615,
        "width": 0.549254,
        "height": 0.187692,
        "name": "",
        "color": ""
    },
    "screen": {              // 这个字段只有click和search的时候才会传
        "screenWidth": 398,  // 图片的真实宽高
        "screenHeight": 772,
     },
    "text": "121212",        // 搜索的文本，只有search的时候才会传
    "direction": "down",     // up或者down，只有scroll会传
    "length": 1080           // 唯一的向量值，只有scroll会传，这里传的是绝对值
}
```

- **响应参数**:

```json
{
  "data": {
    "url": "http://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/35b76134bca77625a51de6c3ccd69aa8.webp",  // 类似这样可以内网直接访问的绝对cos链接
    "domXML": "xxx",    // 页面的dom xml信息
    "screenWidth": 398, // 图片的真实宽高
    "screenHeight": 772
  },
  "retcode": 200,
  "msg": "success"
}
```

### 3. 关闭沙箱接口

- **URL**: `http://mmfinderdrsandboxagentsvr.production.polaris:80/v1/sandbox/annotations/kill`
- **方法**: `POST`
- **请求参数**:

```json
{
   "rtx": "dreamshan",      // 操作人
   "appId": "",             // 小程序id
   "targetId": "沙箱唯一的id", // 沙箱唯一id
}
```

- **响应参数**:

```json
{
  "retcode": 200,
  "msg": "success",
  "data": {}
}
```

### 4. 提交标注内容接口

- **URL**: `http://mmfinderdrsandboxagentsvr.production.polaris:80/v1/sandbox/annotations/submit`
- **方法**: `POST`
- **请求参数**:

```json
{
   "rtx": "dreamshan",      // 操作人
   "appId": "",             // 小程序id
   "targetId": "沙箱唯一的id", // 沙箱唯一id
   "instruction": "我想要一杯奶茶", // 指令
   "operations": [{         // 操作记录列表
       "type": "click",     // click、search、scroll、start、finish
       "markConfig": {      // 这个字段只有click和search的时候才会传（这些内容回显的时候需要，在列表接口中需要全部透传）
            "uuid": "56f75655-76cb-43d5-3859-d6e9eaa65ecb-1747191890406",
            "x": 0.185075,
            "y": 0.244615,
            "width": 0.549254,
            "height": 0.187692,
            "name": "",
            "color": ""
        },
        "text": "121212",   // 搜索的文本，只有search的时候才会传
        "direction": "down", // up或者down，只有scroll会传
        "length": 1080,     // 唯一的向量值，只有scroll会传，这里传的是绝对值
        "url": "http://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/35b76134bca77625a51de6c3ccd69aa8.webp", // 类似这样可以内网直接访问的绝对cos链接
        "domXML": "xxx",    // 页面的dom xml信息
        "screenWidth": 398, // 图片的真实宽高
        "screenHeight": 772
    }]
}
```

- **响应参数**:

```json
{
  "retcode": 200,
  "msg": "success",
  "data": {}
}
```

### 5. 获取标注内容列表接口

- **URL**: `http://mmfinderdrsandboxagentsvr.production.polaris:80/v1/sandbox/annotations`
- **方法**: `GET`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| rtx | string | 否 | 操作人(筛选项)，支持以逗号分隔的多个rtx值，例如：rtx=user1,user2,user3 |
| appId | string | 否 | 小程序id（筛选项） |
| limit | int | 否 | 分页参数，默认10 |
| offset | int | 否 | 分页参数，默认0 |
| isDeleted | int | 否 | 是否已删除，0表示未删除，1表示已删除，不传则不筛选（返回所有记录） |
| startTime | string | 否 | 提交时间范围筛选（开始时间），格式：2006-01-02 15:04:05 |
| endTime | string | 否 | 提交时间范围筛选（结束时间），格式：2006-01-02 15:04:05 |

### 6. 软删除标注内容接口

- **URL**: `http://mmfinderdrsandboxagentsvr.production.polaris:80/v1/sandbox/annotations/delete`
- **方法**: `POST`
- **请求参数**:

```json
{
   "rtx": "dreamshan",      // 操作人
   "appId": "",             // 小程序id
   "targetId": "沙箱唯一的id", // 沙箱唯一id
   "id": 123                // 标注记录ID
}
```

- **响应参数**:

```json
{
  "retcode": 200,
  "msg": "success",
  "data": {}
}
```

- **响应参数**:

```json
{
  "retcode": 200,
  "msg": "success",
  "data": {
      "annotations": [{
          "rtx": "dreamshan",
          "appId": "",
          "instruction": "我想要一杯奶茶", // 指令
          "operations": [{
               "type": "click", // click、search、scroll、start、finish
               "markConfig": {  // 这个字段只有click和search的时候才会传（这些内容回显的时候需要，在列表接口中需要全部透传）
                    "uuid": "56f75655-76cb-43d5-3859-d6e9eaa65ecb-1747191890406",
                    "x": 0.185075,
                    "y": 0.244615,
                    "width": 0.549254,
                    "height": 0.187692,
                    "name": "",
                    "color": ""
                },
                "text": "121212", // 搜索的文本，只有search的时候才会传
                "direction": "down", // up或者down，只有scroll会传
                "length": 1080, // 唯一的向量值，只有scroll会传，这里传的是绝对值
                "url": "http://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/35b76134bca77625a51de6c3ccd69aa8.webp", // 类似这样可以内网直接访问的绝对cos链接
                "domXML": "xxx", // 页面的dom xml信息
                "screenWidth": 398, // 图片的真实宽高
                "screenHeight": 772
            }]
      }]
  }
}
```

## 使用示例

### 使用curl测试接口

1. 启动沙箱：

```bash
curl -X POST http://21.90.4.109/v1/sandbox/annotations/create \
  -H "Content-Type: application/json" \
  -d '{"rtx":"dreamshan","appId":"wx123456","userId":"user123"}'
```

2. 沙箱交互（点击操作）：

```bash
curl -X POST http://21.90.4.109/v1/sandbox/annotations/action \
  -H "Content-Type: application/json" \
  -d '{"rtx":"dreamshan","appId":"wx123456","userId":"user123","targetId":"12121212-12121","type":"click","markConfig":{"uuid":"56f75655-76cb-43d5-3859-d6e9eaa65ecb-1747191890406","x":0.185075,"y":0.244615,"width":0.549254,"height":0.187692,"name":"","color":""},"screen":{"screenWidth":398,"screenHeight":772}}'
```

3. 关闭沙箱：

```bash
curl -X POST http://21.90.4.109/v1/sandbox/annotations/kill \
  -H "Content-Type: application/json" \
  -d '{"rtx":"dreamshan","appId":"wx123456","targetId":"12121212-12121"}'
```

4. 提交标注内容：

```bash
curl -X POST http://21.90.4.109/v1/sandbox/annotations/submit \
  -H "Content-Type: application/json" \
  -d '{"rtx":"dreamshan","appId":"wx123456","targetId":"12121212-12121","instruction":"我想要一杯奶茶","submitTime":"2025-01-01 12:00:00","operations":[{"type":"click","markConfig":{"uuid":"56f75655-76cb-43d5-3859-d6e9eaa65ecb-1747191890406","x":0.185075,"y":0.244615,"width":0.549254,"height":0.187692,"name":"","color":""},"url":"http://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/35b76134bca77625a51de6c3ccd69aa8.webp","domXML":"xxx","screenWidth":398,"screenHeight":772}]}'
```

5. 获取标注内容列表：

```bash
# 查询未删除的记录
curl -X GET "http://21.90.4.109/v1/sandbox/annotations?rtx=dreamshan&appId=wx123456&limit=10&offset=0&isDeleted=0"

# 查询已删除的记录
curl -X GET "http://21.90.4.109/v1/sandbox/annotations?rtx=dreamshan&appId=wx123456&limit=10&offset=0&isDeleted=1"

# 查询所有记录（不区分是否删除）
curl -X GET "http://21.90.4.109/v1/sandbox/annotations?rtx=dreamshan&appId=wx123456&limit=10&offset=0"

# 查询多个用户的记录
curl -X GET "http://21.90.4.109/v1/sandbox/annotations?rtx=user1,user2,user3&appId=wx123456&limit=10&offset=0"

# 按提交时间范围查询记录
curl -X GET "http://21.90.4.109/v1/sandbox/annotations?rtx=dreamshan&appId=wx123456&startTime=2023-01-01%2000:00:00&endTime=2023-12-31%2023:59:59"
```

6. 软删除标注内容：

```bash
curl -X POST http://21.90.4.109/v1/sandbox/annotations/delete \
  -H "Content-Type: application/json" \
  -d '{"rtx":"dreamshan","appId":"wx123456","targetId":"12121212-12121","id":123}'
```



