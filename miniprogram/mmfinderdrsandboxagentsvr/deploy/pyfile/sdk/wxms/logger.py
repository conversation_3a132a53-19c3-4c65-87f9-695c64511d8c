"""
日志类
"""

import json
import logging
import os
import sys
from copy import copy
from logging.handlers import <PERSON><PERSON><PERSON><PERSON><PERSON>, Queue<PERSON><PERSON>ener
from multiprocessing import Queue
from uuid import UUID

import click
import httpx
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.logging import LoggingInstrumentor
from opentelemetry.instrumentation.openai import OpenAIInstrumentor
from opentelemetry.instrumentation.openai.shared import chat_wrappers
from opentelemetry.instrumentation.redis import RedisInstrumentor
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from wxms.env import CONF_ENV, HOSTNAME, MODULE, POD_IP


class ColourizedFormatter(logging.Formatter):
    """
    日志内容上颜色模块
    """

    level_name_colors = {
        logging.DEBUG: lambda field: click.style(str(field), fg="bright_cyan"),
        logging.WARNING: lambda field: click.style(str(field), fg="bright_yellow"),
        logging.ERROR: lambda field: click.style(str(field), fg="bright_red"),
        logging.CRITICAL: lambda field: click.style(str(field), fg="bright_red"),
    }

    def formatMessage(self, record: logging.LogRecord) -> str:
        recordcopy = copy(record)
        # message
        if "color_message" in recordcopy.__dict__:
            recordcopy.msg = recordcopy.__dict__["color_message"]
            recordcopy.__dict__["message"] = recordcopy.getMessage()
        recordcopy.__dict__["message"] = self.__color_field_by_level_no(
            recordcopy.message, recordcopy.levelno
        )
        # asctime
        recordcopy.__dict__["asctime"] = self.__color_field_by_fg(
            recordcopy.asctime, "bright_green"
        )
        # levelname
        seperator = " " * (8 - len(recordcopy.levelname))
        levelname = self.__color_field_by_level_no(
            recordcopy.levelname, recordcopy.levelno
        )
        recordcopy.__dict__["levelname"] = levelname + seperator
        # otelTraceID
        seperator = " " * (32 - len(recordcopy.otelTraceID))  # type: ignore
        recordcopy.__dict__["otelTraceID"] = recordcopy.otelTraceID + seperator  # type: ignore
        # otelSpanID
        seperator = " " * (16 - len(recordcopy.otelSpanID))  # type: ignore
        recordcopy.__dict__["otelSpanID"] = recordcopy.otelSpanID + seperator  # type: ignore
        # otelTraceSampled
        seperator = " " * (5 - len(str(recordcopy.otelTraceSampled)))  # type: ignore
        recordcopy.__dict__["otelTraceSampled"] = (
            str(recordcopy.otelTraceSampled) + seperator  # type: ignore
        )
        # name
        recordcopy.__dict__["name"] = self.__color_field_by_fg(
            recordcopy.name, "bright_magenta"
        )
        # module
        recordcopy.__dict__["module"] = self.__color_field_by_fg(
            recordcopy.module, "bright_blue"
        )
        # funcName
        recordcopy.__dict__["funcName"] = self.__color_field_by_fg(
            recordcopy.funcName, "bright_blue"
        )
        # lineno
        recordcopy.__dict__["lineno"] = self.__color_field_by_fg(
            str(recordcopy.lineno), "bright_blue"
        )
        # customized_data_info
        customized_data_info = "{}"
        if hasattr(recordcopy, "customized_data_info"):
            try:
                customized_data_info = json.dumps(
                    recordcopy.customized_data_info, ensure_ascii=False  # type: ignore
                )
            except Exception:  # pylint: disable=W0718
                customized_data_info = recordcopy.customized_data_info  # type: ignore
        recordcopy.__dict__["customized_data_info"] = self.__color_field_by_level_no(
            field=customized_data_info,
            level_no=recordcopy.levelno,
            default_fg="bright_green",
        )
        return super().formatMessage(recordcopy)

    def __color_field_by_fg(self, field: str, fg: str) -> str:
        return click.style(str(field), fg=fg)

    def __color_field_by_level_no(
        self, field: str, level_no: int, default_fg: str = "reset"
    ) -> str:
        def default(field: str) -> str:
            return click.style(str(field), fg=default_fg)

        func = self.level_name_colors.get(level_no, default)
        return func(field)


class NoneRootWarningFilter(logging.Filter):
    """
    非 root 的 logger 只允许 WARNING 级别的日志
    """

    def filter(self, record):
        return record.name == "root" or record.levelno >= logging.WARNING


class WecubeHandler(logging.Handler):
    """
    上传日志到 wecube
    """

    def __init__(self, is_test_env: bool, report_ip: str, report_module: str):
        super().__init__()
        self.biz_id = 16225
        self.is_test_env = is_test_env
        self.report_ip = report_ip
        self.report_module = report_module
        self.client = httpx.Client(
            limits=httpx.Limits(
                max_connections=100,  # 最大连接数
                max_keepalive_connections=50,  # 最大保持活跃的连接数
                keepalive_expiry=30,  # 保持连接的时间（秒）
            ),
            timeout=30.0,  # 超时设置
        )

    def emit(self, record: logging.LogRecord):
        # workflow_run_id
        workflow_run_id = (
            str(UUID(record.otelTraceID)) if len(record.otelTraceID) == 32 else ""  # type: ignore
        )
        # customized_data_info
        customized_data_info = "{}"
        if hasattr(record, "customized_data_info"):
            try:
                customized_data_info = json.dumps(
                    record.customized_data_info, ensure_ascii=False  # type: ignore
                )
            except Exception:  # pylint: disable=W0718
                customized_data_info = record.customized_data_info  # type: ignore
        data = [
            {
                "biz_id": self.biz_id,
                "report_host_role": 1 if self.is_test_env else 0,
                "time": int(record.created),
                "report_ip": self.report_ip,
                "report_module": self.report_module,
                "level": record.levelname,
                "traceID": record.otelTraceID,  # type: ignore
                "SpanID": record.otelSpanID,  # type: ignore
                "sampled": str(record.otelTraceSampled),  # type: ignore
                "workflow_run_id": workflow_run_id,
                "data": record.message,
                "caller": f"{record.module}:{record.funcName}:{record.lineno}",
                "params": customized_data_info,
                "timestamp": int(record.created * 1000 * 1000),
            }
        ]
        try:
            self.client.request(
                method="POST",
                url="http://api.cube.woa.com/cube/report/reportbizdata?f=json",
                headers={"Content-Type": "application/json"},
                json=data,
            )
        except Exception as e:  # pylint: disable=W0718
            sys.stderr.write(f"WecubeHandler emit failed: {e}")


async def _process_image_item(
    item, trace_id, span_id, message_index, content_index
):  # pylint: disable=W0613
    customized_url_for_trace = item.get("image_url", {}).get(
        "customized_url_for_trace", ""
    )
    if customized_url_for_trace:
        return {"type": "image_url", "image_url": {"url": customized_url_for_trace}}
    else:
        return item


# 不优雅的解决方案，如果要优雅的话需要开源社区支持
chat_wrappers._process_image_item = _process_image_item  # pylint: disable=W0212

# 获取环境变量
app_name = MODULE
app_env = CONF_ENV
hostname = HOSTNAME
ip = POD_IP
# 日志配置
format_str = "%(asctime)s | %(levelname)-8s | trace_id=%(otelTraceID)-32s span_id=%(otelSpanID)-16s sampled=%(otelTraceSampled)-5s | %(name)s | %(module)s:%(funcName)s:%(lineno)s - %(message)s | %(customized_data_info)s"  # pylint: disable=C0103,C0301
log_config = {
    "version": 1,
    "loggers": {
        "uvicorn": {"level": "INFO", "propagate": True},
        "uvicorn.error": {"level": "INFO", "propagate": True},
        "uvicorn.access": {"level": "INFO", "propagate": True},
    },
}
resource = Resource.create(
    attributes={
        "service_name": app_name,
        "service_env": app_env,
        "service.name": app_name,
        "service.env": app_env,
    }
)
# trace Tempo 配置
os.environ["OTEL_EXPORTER_OTLP_TRACES_PROTOCOL"] = "http/protobuf"
os.environ["OTEL_EXPORTER_OTLP_ENDPOINT"] = (
    "http://api.wetelemetry.woa.com/otlptracehttp"
)
# os.environ["OTEL_EXPORTER_OTLP_HEADERS"] = "Authorization=Basic%20"
os.environ["OTEL_PYTHON_LOG_CORRELATION"] = "true"
os.environ["OTEL_TRACES_SAMPLER"] = "traceidratio"
os.environ["OTEL_TRACES_SAMPLER_ARG"] = "1.00"
tracer_provider = TracerProvider(resource=resource)
tracer_provider.add_span_processor(BatchSpanProcessor(OTLPSpanExporter()))
# 设置 trace
trace.set_tracer_provider(tracer_provider)
# 初始化 root logger
logger = logging.getLogger()
logger.setLevel(logging.DEBUG)
logger.addFilter(NoneRootWarningFilter())
logger.handlers.clear()
formatter = ColourizedFormatter(fmt=format_str)
# 设置 logger 的控制台输出
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_handler.addFilter(NoneRootWarningFilter())
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)
# 设置 logger 的文件输出（追加写入）
file_handler = logging.FileHandler(os.path.dirname(__file__) + "/wxms.log", mode="a")
file_handler.setLevel(logging.DEBUG)
file_handler.addFilter(NoneRootWarningFilter())
file_handler.setFormatter(formatter)
# 设置 logger 的 wecube 输出
wecube_handler = WecubeHandler(
    is_test_env=app_env != "online",
    report_ip=ip,
    report_module=app_name,
)
wecube_handler.setLevel(logging.INFO)
wecube_handler.addFilter(NoneRootWarningFilter())
# 使用 Python 官方推荐的异步日志方案，注意程序结束前需要调用 queue_listener.stop()
report_queue = Queue(-1)
queue_handler = QueueHandler(report_queue)
queue_handler.setLevel(logging.DEBUG)
queue_handler.addFilter(NoneRootWarningFilter())
queue_listener = QueueListener(
    report_queue, file_handler, wecube_handler, respect_handler_level=True
)
queue_listener.start()
logger.addHandler(queue_handler)
# 设置 Instrumentor
LoggingInstrumentor().instrument(set_logging_format=True, log_format=format_str)
OpenAIInstrumentor().instrument(tracer_provider=tracer_provider)
RedisInstrumentor().instrument(tracer_provider=tracer_provider)


def close_logger():
    """
    确保日志和 trace 都写入完成
    """
    queue_listener.stop()
    tracer_provider.shutdown()


if __name__ == "__main__":
    from typing import Generic, TypeVar

    from pydantic import BaseModel, Field

    T = TypeVar("T")

    class TestClass(BaseModel, Generic[T]):
        """
        用来测试日志输出的类
        """

        code: int = Field(..., title="业务相关的 code")
        message: str = Field(..., title="业务相关的信息")
        data: T = Field(..., title="数据")

    a = TestClass[str](code=0, message="testa", data="testa")
    b = TestClass[str](code=0, message="testb", data="testb")
    logger.info(
        "logger init success",
        extra={
            "customized_data_info": {
                "test_list": [1, 2, 3],
                # "test_object_list": [a, b],  # bad
                "test_object_str_list": [a.model_dump_json(), b.model_dump_json()],
                "test_dict": {"a": 1, "b": 2, "c": 3},
                # "test_object_dict": {"a": a, "b": b},  # bad
                "test_object_str_dict": {
                    "a": a.model_dump_json(),
                    "b": b.model_dump_json(),
                },
                "test_none": None,
                # "test_bytes": "test_bytes".encode(),  # bad
                "test_bytes_str": str("test_bytes".encode()),
            }
        },
    )
    close_logger()
