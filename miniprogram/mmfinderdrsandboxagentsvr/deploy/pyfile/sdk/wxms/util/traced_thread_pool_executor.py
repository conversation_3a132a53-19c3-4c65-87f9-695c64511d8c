"""
带有 trace 信息的线程池
"""

from concurrent.futures import Thread<PERSON><PERSON><PERSON>xecutor
from typing import Callable

from opentelemetry import context


class TracedThreadPoolExecutor(ThreadPoolExecutor):
    """
    Implementation of :class:`ThreadPoolExecutor` that will pass context into sub tasks.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def submit(self, fn, /, *args, **kwargs):
        # get the current otel context
        caller_context = context.get_current()
        if caller_context:
            return super().submit(
                lambda: self.__with_otel_context(
                    caller_context, lambda: fn(*args, **kwargs)
                ),
            )
        else:
            return super().submit(lambda: fn(*args, **kwargs))

    def __with_otel_context(self, caller_context: context.Context, fn: Callable):
        context.attach(caller_context)
        return fn()
