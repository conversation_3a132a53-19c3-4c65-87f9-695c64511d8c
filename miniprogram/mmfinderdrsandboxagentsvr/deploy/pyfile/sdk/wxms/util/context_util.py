"""
上下文工具类
"""

import json
import secrets
import traceback
from functools import wraps

from opentelemetry import trace
from opentelemetry.trace.propagation.tracecontext import TraceContextTextMapPropagator
from starlette_context import context
from wxms.logger import logger
from wxms.util.common_util import CustomJSONEncoder, common_util
from wxms.util.time_util import time_util


class ContextUtil:
    """
    上下文工具类
    """

    def __init__(self):
        pass

    def add_trace_span(
        self,
        span_name: str | None = None,
        carrier: dict | None = None,
        attributes: dict | None = None,
        record_input: bool = False,
        record_output: bool = False,
    ):
        """
        函数装饰器，用于将函数包装为一个 span（同步）

        :param span_name: span name，不传时默认为函数名
        :param carrier: 带有上下文信息的字典
        :param attributes: span 的 attributes
        :param record_input: 是否记录被装饰函数的输入为 span 的 attribution
            当为 True 时，要求输入可以被 json.dumps，输入仅记录 kwargs
        :param record_output: 是否记录被装饰函数的输出为 span 的 attribution
            当为 True 时，要求输出可以被 json.dumps
        """

        def add_trace_span_wrapper(func):
            @wraps(func)
            def add_trace_span_inner(*args, **kwargs):
                tracer = trace.get_tracer(__name__)
                span_name_value = span_name if span_name else func.__name__
                attributes_value = {}
                if attributes is not None:
                    attributes_value = {
                        f"{span_name_value}.{k}": attributes[k] for k in attributes
                    }
                if record_input:
                    attributes_value["func_input"] = json.dumps(
                        kwargs, cls=CustomJSONEncoder
                    )
                with tracer.start_as_current_span(
                    span_name_value,
                    context=(
                        TraceContextTextMapPropagator().extract(carrier)
                        if carrier is not None
                        else None
                    ),
                    attributes=attributes_value if attributes_value else None,
                ) as span:
                    res = func(*args, **kwargs)
                    if record_output:
                        span.add_event(
                            name="func_output",
                            attributes={
                                "name": "func_output",
                                "data": json.dumps(res, cls=CustomJSONEncoder),
                            },
                        )
                return res

            return add_trace_span_inner

        return add_trace_span_wrapper

    def add_trace_span_for_async(
        self,
        span_name: str | None = None,
        carrier: dict | None = None,
        attributes: dict | None = None,
        record_input: bool = False,
        record_output: bool = False,
    ):
        """
        函数装饰器，用于将函数包装为一个 span（异步）

        :param span_name: span name，不传时默认为函数名
        :param carrier: 带有上下文信息的字典
        :param attributes: span 的 attributes
        :param record_input: 是否记录被装饰函数的输入为 span 的 attribution
            当为 True 时，要求输入可以被 json.dumps，输入仅记录 kwargs
        :param record_output: 是否记录被装饰函数的输出为 span 的 attribution
            当为 True 时，要求输出可以被 json.dumps
        """

        def add_trace_span_wrapper(func):
            @wraps(func)
            async def add_trace_span_inner(*args, **kwargs):
                tracer = trace.get_tracer(__name__)
                span_name_value = span_name if span_name else func.__name__
                attributes_value = {}
                if attributes is not None:
                    attributes_value = {
                        f"{span_name_value}.{k}": attributes[k] for k in attributes
                    }
                if record_input:
                    attributes_value["func_input"] = json.dumps(
                        kwargs, cls=CustomJSONEncoder
                    )
                with tracer.start_as_current_span(
                    span_name_value,
                    context=(
                        TraceContextTextMapPropagator().extract(carrier)
                        if carrier is not None
                        else None
                    ),
                    attributes=attributes_value if attributes_value else None,
                ) as span:
                    res = await func(*args, **kwargs)
                    if record_output:
                        span.add_event(
                            name="func_output",
                            attributes={
                                "name": "func_output",
                                "data": json.dumps(res, cls=CustomJSONEncoder),
                            },
                        )
                return res

            return add_trace_span_inner

        return add_trace_span_wrapper

    def generate_traceparent(self) -> str:
        """
        随机生成 traceparent，例如 00-6688363df60b8e3b80a48219010de3e2-bd02215e6cd47747-01

        :return: traceparent
        """
        trace_id = secrets.token_hex(16)
        span_id = secrets.token_hex(8)
        # 01 表示采样
        # 00 表示不采样
        flags = "01"
        return f"00-{trace_id}-{span_id}-{flags}"

    def get_and_inject_span_context(self, carrier: dict | None = None) -> dict:
        """
        向指定的字典 carrier 注入带有上下文信息的字段

        :param carrier: 指定的字典，如果为 None 时会初始化一个空字典来被注入
        :return: 注入带有上下文信息的字段后的 carrier，当入参的 carrier 不为 None 时，二者是同一个值
        """
        if carrier is None:
            carrier = {}
        TraceContextTextMapPropagator().inject(carrier)
        return carrier

    def get_data_from_context(self, key: str) -> str:
        """
        从上下文信息中获取数据

        :param key: 上下文信息中的 key 值
        :return: 上下文信息中 key 值对应的 value
        """
        res = ""
        try:
            res = context.data.get(key, "")
        except Exception as e:  # pylint: disable=W0718
            logger.warning(
                "get context failed",
                extra={
                    "customized_data_info": {
                        "key": key,
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
        return res

    def get_span_id(self) -> str:
        """
        获取 span id

        :return: span id
        """
        current_span = trace.get_current_span()
        span_id = current_span.get_span_context().span_id
        return trace.format_span_id(span_id)

    def get_trace_id(self, mock_fake_trace_id: bool = False) -> str:
        """
        获取 trace id

        :param mock_fake_trace_id: 当 trace id 未初始化设置时，是否需要 mock 一个假的 trace id
        :return: trace id
        """
        current_span = trace.get_current_span()
        trace_id = current_span.get_span_context().trace_id
        res = trace.format_trace_id(trace_id)
        if mock_fake_trace_id and res == "00000000000000000000000000000000":
            res += str(time_util.get_millisecond_timestamp_of_current_time())
            res += common_util.get_random_id(8)
            res = res[-32:]
        return res

    def set_data_into_context(self, key: str, value: str) -> bool:
        """
        设置上下文信息中的数据

        :param key: 上下文信息中的 key 值
        :param value: 上下文信息中 key 值对应的 value
        :return: 是否设置成功
        """
        res = True
        try:
            context.data[key] = value
        except Exception as e:  # pylint: disable=W0718
            res = False
            logger.warning(
                "set context failed",
                extra={
                    "customized_data_info": {
                        "key": key,
                        "value": value,
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
        return res


context_util = ContextUtil()

if __name__ == "__main__":
    logger.info(context_util.get_trace_id(mock_fake_trace_id=True))
    logger.info(context_util.get_and_inject_span_context())
    logger.info(context_util.generate_traceparent())
