"""
time 工具类
"""

import asyncio
import functools
import time
from datetime import datetime, timedelta, timezone
from typing import Awaitable, Callable, ParamSpec, TypeVar

from func_timeout import func_set_timeout
from opentelemetry import context

beijing_tz = timezone(timedelta(hours=8))
P = ParamSpec("P")  # 用于捕获函数参数的类型
R = TypeVar("R")  # 用于捕获函数返回值的类型


class TimeUtil:
    """
    time 工具类
    """

    def __init__(self):
        pass

    def execute_function_with_timeout(
        self,
        timeout: int,
        func: Callable[P, R],
        /,
        *args: P.args,
        **kwargs: P.kwargs,
    ) -> R:
        """
        在 timeout 时间内运行函数，并通过闭包保证 context 传递

        :param timeout: 超时时间，单位秒
        :param func: 被执行函数
        :param *args: 被执行函数的入参
        :param **kwargs: 被执行函数的入参
        :return: 被执行函数的返回
        :raises func_timeout.exceptions.FunctionTimedOut: 如果超时则会抛出该异常，需要特别处理
        """
        # get the current otel context
        caller_context = context.get_current()

        @func_set_timeout(timeout=timeout)
        @functools.wraps(func)
        def inner():
            if caller_context:
                context.attach(caller_context)
            return func(*args, **kwargs)

        return inner()

    async def execute_function_with_timeout_async(
        self,
        timeout: int,
        func: Callable[P, Awaitable[R]],
        /,
        *args: P.args,
        **kwargs: P.kwargs,
    ) -> R:
        """
        在 timeout 时间内运行函数

        :param timeout: 超时时间，单位秒
        :param func: 被执行函数
        :param *args: 被执行函数的入参
        :param **kwargs: 被执行函数的入参
        :return: 被执行函数的返回
        :raises asyncio.TimeoutError: 如果超时则会抛出该异常，需要特别处理
        """
        return await asyncio.wait_for(
            func(*args, **kwargs),
            timeout=timeout,
        )

    def get_datetime_str_from_millisecond_timestamp(
        self, a: int | None, fmt: str = "%Y-%m-%d %H:%M:%S", tz=beijing_tz
    ) -> str:
        """
        将毫秒级时间戳按格式输出为时间字符串

        :param a: 毫秒级时间戳，当为 None 时返回空字符串
        :param fmt: 时间字符串的格式
        :param tz: 时区
        :return: 时间字符串
        """
        if a is None or not a:
            return ""
        utc_date_time = datetime.fromtimestamp(a / 1000, tz=tz)
        return utc_date_time.strftime(fmt)

    def get_millisecond_timestamp_of_current_time(self) -> int:
        """
        获取当前时间的毫秒级时间戳

        :return: 当前时间的毫秒级时间戳
        """
        return int(round(time.time() * 1000))

    def get_spend_time_str_from_millisecond(self, a: int) -> str:
        """
        转换毫秒级计时为时间字符串

        :param a: 毫秒级计时
        :return: 计时的时间字符串
        """
        spend_time = a // 1000
        s = spend_time % 60
        spend_time = spend_time // 60
        m = spend_time % 60
        h = spend_time // 60
        if h:
            return f"{h}小时{m}分钟{s}秒"
        elif m:
            return f"{m}分钟{s}秒"
        else:
            return f"{s}秒"

    def get_utc_datetime_from_millisecond_timestamp(self, a: int) -> datetime:
        """
        转换毫秒级时间戳为 utc 的 datetime

        :param a: 毫秒级时间戳
        :return: utc 的 datetime
        """
        res = datetime.fromtimestamp(a / 1000, tz=timezone.utc)
        if res.microsecond == 0:
            res = res.replace(microsecond=1)
        return res

    def get_yesterday_end_timestamp(self) -> int:
        """
        获取昨天最后一秒的秒级时间戳

        :return: 昨天最后一秒的秒级时间戳
        """
        now = datetime.now()
        res = datetime(now.year, now.month, now.day) - timedelta(seconds=1)
        return int(res.timestamp())

    def get_yesterday_start_timestamp(self) -> int:
        """
        获取昨天最早一秒的秒级时间戳

        :return: 昨天最早一秒的秒级时间戳
        """
        now = datetime.now()
        res = datetime(now.year, now.month, now.day) - timedelta(days=1)
        return int(res.timestamp())


time_util = TimeUtil()

if __name__ == "__main__":
    import traceback

    from func_timeout.exceptions import FunctionTimedOut
    from wxms.logger import logger

    logger.info(time_util.get_utc_datetime_from_millisecond_timestamp(1749464705000))
    logger.info(time_util.get_utc_datetime_from_millisecond_timestamp(1735032623001))
    logger.info(time_util.get_yesterday_start_timestamp())
    logger.info(time_util.get_yesterday_end_timestamp())

    logger.info("start")
    try:
        time_util.execute_function_with_timeout(2, time.sleep, 3)
    except FunctionTimedOut as e:
        logger.warning(
            "execute_function_with_timeout failed",
            extra={
                "customized_data_info": {
                    "exception": str(e),
                    "traceback": traceback.format_exc(),
                }
            },
        )
    logger.info("end")
    loop = asyncio.get_event_loop()
    logger.info("start")
    try:
        loop.run_until_complete(
            time_util.execute_function_with_timeout_async(2, asyncio.sleep, 3)
        )
    except asyncio.TimeoutError as e:
        logger.warning(
            "execute_function_with_timeout_async failed",
            extra={
                "customized_data_info": {
                    "exception": str(e),
                    "traceback": traceback.format_exc(),
                }
            },
        )
    logger.info("end")
