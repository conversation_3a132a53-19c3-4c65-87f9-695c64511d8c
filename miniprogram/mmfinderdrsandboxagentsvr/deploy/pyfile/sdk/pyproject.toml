[tool.poetry]
name = "wxms"
version = "2.84.0"
description = "A Python package for interacting with WeChat Mini Programs via CDP (Chrome DevTools Protocol)."
authors = ["mikexinchen <<EMAIL>>", "yuy<PERSON><PERSON><PERSON> <yuyi<PERSON><EMAIL>>"]
license = "MIT"

[tool.poetry.dependencies]
click = "^8.2.1"
cos_python_sdk_v5 = "^1.9.37"
func_timeout = "^4.3.5"
httpx = "^0.28.1"
openai = "^1.83.0"
opentelemetry-exporter-otlp-proto-http = "^1.34.0"
opentelemetry-instrumentation-logging = "^0.55b0"
opentelemetry-instrumentation-openai = "^0.40.8"
opentelemetry-instrumentation-redis = "^0.55b0"
pillow = "^11.2.1"
polaris-cpp-py = "^0.1.5"
pre-commit = "^4.2.0"
py-mini-racer = "^0.6.0"
pydantic = "^2.11.5"
pydantic-settings = "^2.9.1"
python = "^3.12"
redis = "^6.2.0"
starlette-context = "^0.4.0"

[[tool.poetry.source]]
name = "tencent_pypi"
url = "https://mirrors.cloud.tencent.com/pypi/simple/"
priority = "primary"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
