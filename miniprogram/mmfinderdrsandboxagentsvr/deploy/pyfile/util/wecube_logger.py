import atexit
import copy
import fcntl
import inspect
import json
import os
import socket
import struct
import sys
import threading
import time
from urllib import request


def get_caller_info(root_dir=None):
    """
    获取调用点的相对路径和行号
    :param root_dir: 项目根目录（绝对路径）
    :return: 格式如 "src/example.py:100" 的字符串
    """
    # 获取调用栈（跳过当前函数的栈帧）
    stack = inspect.stack()
    # 第1个是当前函数（get_caller_info），第2个是调用者的调用位置
    frame_info = stack[2]
    abs_path = os.path.abspath(frame_info.filename)
    lineno = frame_info.lineno

    # 计算相对路径
    if root_dir is None:
        # 默认使用当前工作目录作为根目录
        root_dir = os.getcwd()
    relative_path = os.path.relpath(abs_path, start=root_dir)

    return f"{relative_path}:{lineno}"


def get_ipaddr(ifname):
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    return socket.inet_ntoa(
        fcntl.ioctl(
            s.fileno(), 0x8915, struct.pack("256s", ifname.encode("utf-8")[:15])
        )[20:24]
    )


class EyeReporter(object):
    report_ip = get_ipaddr("eth1")
    is_test_env = False
    instance = None
    max_buffer_size = 128

    def __init__(self, threshold=5, interval=60):
        self.items_buffer = []
        self.interval = interval
        self.lock = threading.Lock()
        self.timer = None
        self._start_periodic()
        atexit.register(self._exit_upload)

    def clear_buffer(self):
        self._report_buffer()

    def _exit_upload(self):
        if self.timer:
            self.timer.cancel()
        self.clear_buffer()

    def _start_periodic(self):
        self.timer = threading.Timer(self.interval, self._periodic_task)
        self.timer.daemon = True
        self.timer.start()

    def _periodic_task(self):
        self.clear_buffer()
        self._start_periodic()

    @staticmethod
    def set_report_ip(ipaddr):
        EyeReporter.report_ip = ipaddr

    @staticmethod
    def set_is_test_env(is_test_env):
        EyeReporter.is_test_env = is_test_env

    @staticmethod
    def set_max_buffer_size(n):
        EyeReporter.max_buffer_size = n

    def _report_buffer(self):
        http_req = request.Request(
            "http://api.cube.woa.com/cube/report/reportbizdata?f=json",
            headers={"Content-Type": "application/json"},
            method="POST",
        )
        exceptions = (Exception,)
        try:
            http_resp = request.urlopen(
                http_req, json.dumps(self.items_buffer).encode("utf-8"), timeout=3
            )
            print(http_resp.read().decode("utf-8"), file=sys.stderr)
        except exceptions as e:
            print(e, file=sys.stderr)
        self.items_buffer = []

    def _push_to_buffer(self, item):
        self.items_buffer.append(item)
        if len(self.items_buffer) >= EyeReporter.max_buffer_size:
            self._report_buffer()

    def report(self, biz_id, data=None, timestamp=0):
        if not timestamp:
            timestamp = int(time.time())
        if not data:
            data = {}
        item = copy.deepcopy(data)
        item["biz_id"] = biz_id
        item["time"] = timestamp
        item["report_ip"] = EyeReporter.report_ip
        if EyeReporter.is_test_env:
            item["report_host_role"] = 1
        return self._push_to_buffer(item)

    @classmethod
    def get_default_instance(cls):
        if EyeReporter.instance is None:
            cls.instance = cls()
        return cls.instance


# EyeReporter.set_is_test_env(True)
EyeReporter.set_max_buffer_size(32)


def custom_log(
    *values: object, biz_id=16225, level="INFO", root_dir=None, params=None, trace_id=""
) -> None:
    caller_info = get_caller_info(root_dir=root_dir)
    data = " ".join(map(str, values))

    nanoseconds = time.time_ns()
    seconds = nanoseconds // 10**9
    microseconds = nanoseconds // 10**3

    eye_reporter = EyeReporter.get_default_instance()
    info = {
        "level": level,
        "traceID": trace_id,
        "SpanID": "",
        "sampled": "",
        "data": data,
        "caller": caller_info,
        "timestamp": microseconds,
    }
    if params is not None:
        info["params"] = json.dumps(params, ensure_ascii=False)
    if eye_reporter is not None:
        eye_reporter.report(biz_id=biz_id, data=info, timestamp=seconds)
