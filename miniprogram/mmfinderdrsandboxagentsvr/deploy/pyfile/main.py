"""
主入口
"""

import argparse
import sys
import traceback

from func_timeout.exceptions import FunctionTimedOut
from model import PythonInput, PythonResult, PythonResultStatus
from task_handler import get_task_agent
from wxms.env import (
    WXMS_BASE_URL,
)
from wxms.logger import close_logger, logger
from wxms.model import (
    InterruptError,
    SandboxActionMessage,
    SandboxActionMessageStyleType,
    SandboxActionStandardConfig,
)
from wxms.service.sandbox_service import SandboxClient
from wxms.util.common_util import common_util
from wxms.util.context_util import context_util
from wxms.util.time_util import time_util

__DEFAULT_PROMPT = """你是一个小程序操作助手，现在需要操作小程序来完成用户的需求，整个过程可能需要进行多步的操作和尝试才能完成
请尽可能的进行尝试和探索来完成用户的需求

## 输入信息
- 用户输入：任务指令或者补充信息，将会包含在<user_input></user_input>标签中
- 小程序状态：根据小程序页面的dom树进行解析和简化后的结构化文本，将会包含在<state_i></state_i>标签中（代表当前操作第i步）
  - 每个可操作的元素开头都会有一个唯一的id标识在[]内（比如`[n]`代表当前元素的id是n）
  - 会用缩进代表页面上的一些层级关系
  - clickable代表元素可进行点击；typeable代表元素可进行输入；scrollable代表元素可进行滑动；有时这些事件也会继承给层级内部的子元素
  - 元素的描述信息有时会使用英文或者中英混杂，这属于正常情况

## 任务
1. 你需要以完成用户的需求为主要目标，对当前小程序的状态进行分析，并从tools中选择合适的tool对小程序进行操作
2. 优先使用搜索能力来查找需要的内容，有的搜索框不会标注typeable，只会有clickable，你需要根据文字描述进行合理的推断
3. 每次操作完成后，你将会收到最新的小程序状态信息
4. 整个操作过程需要反复迭代和尝试，直到任务完成
5. 如果当前时间不在小程序的工作范围内，及时终止并告知用户相关信息

## 注意事项
- 购物车内有其他无关商品时，尝试进行剔除
- 购物、点单、消费任务进入到支付页面即可terminate，无需点击支付操作
- 你只对最新的state中展示的元素编号进行操作
- 如果当页面元素中出现有关：*服务使用条款*、*隐私协议*等信息时，**点击上层/前面的同意按钮**（注意不是条款内容），否则其他的操作（登录、支付等）可能无法进行点击
- 如果发现无法完成任务，转变思路再尝试一下
- 页面状态为空时，wait一段时间后再操作
- 不要擅自篡改或简化用户的需求
- 需要切换位置时，如果没有明确的可操作元素，可尝试点击地点来切换位置
- 如果可以进行搜索，优先考虑使用搜索功能

**请将当前任务状态的总结以及下一步的规划输出在<think></think>标签内，字数小于150字**"""
__DEFAULT_PROMPT_VLT = """
你是一个小程序操作助手，上述图像是按顺序操作任务指令，最近一张是当前图像。
当前指令: 如果当前页面能够搜索优先使用搜索功能来实现指令，{}，请问在当前图像里我要怎么操作？当前只能有一种输出选项：只能回复接下来的一个动作，不能回复多个动作，并给出在页面的精确像素坐标（x y），最终输出格式严格遵循<think>xxx</think><answer>xxx</answer>这个格式。answer动作只能在以下6 类集合选择：1. click x y(代表点击像素坐标 x,y)，2. input x y t（代表在x y 选中输入框并输入文本t） 3. finish（代表任务成功结束） 4. stop（代表任务无法进行 进程终止）5. scroll x（代表下滑x个像素，x为负数代表上滑|x|个像素）6. wait t（代表当前页面处于加载中，可以等待t秒）。 思考内容：首先通过对话上下文判断上一步的操作是否生效，如果上一步操作无效，需要反思并采取正确的动作，例如 1.  input x y t 后页面是否有显示 input 的内容t，没有的话则操作未生效，考虑网页不支持本步input 而应该采取click x y 操作使用点击小程序拉起的键盘输入 2. click x y 后操作未生效考虑点击位置无效，应该采取点击其他位置click x1 y1实现指令 3. assistant重复输入相同命令表示当前操作无效，需要采取其他方法实现指令 4. 需要详细检查 input 的内容 t 是否正确显示在页面中 5. 最近两张图像没有变化时需要反思动作是否正确
"""
__DEFAULT_PROMPT_VLT_V1 = """你是一个小程序操作助手，上述图像是按顺序操作任务指令，最近一张是当前图像。
当前指令: {}，用户历史操作总结为:{}，请问在当前图像里我要怎么操作？当前只能有一种输出选项：只能回复接下来的一个动作，不能回复多个动作，并给出在页面的精确像素坐标（x y），最终输出格式严格遵循<think>xxx</think><answer>xxx</answer><summary>xxx</summary>这个格式。

思考过程放在<think>、</think>标签内。思考内容：首先通过对话上下文判断上一步的操作是否生效，如果上一步操作无效，需要反思并采取正确的动作，例如 1.  input x y t 后页面是否有显示 input 的内容t，没有的话则操作未生效，考虑网页不支持本步input 而应该采取click x y 操作使用点击小程序拉起的键盘输入 2. click x y 后操作未生效考虑点击位置无效，应该采取点击其他位置click x1 y1实现指令 3. assistant重复输入相同命令表示当前操作无效，需要采取其他方法实现指令 4. 需要详细检查 input 的内容 t 是否正确显示在页面中 5. 最近两张图像没有变化时需要反思动作是否正确。

操作动作放在<answer>、</answer>标签内。操作动作只能在以下6 类集合选择：1. click x y(代表点击像素坐标 x,y)，2. input x y t（代表在x y 选中输入框并输入文本t） 3. finish（代表任务成功结束） 4. stop（代表任务无法进行 进程终止）5. scroll x（代表下滑x个像素，x为负数代表上滑|x|个像素）6. wait t（代表当前页面处于加载中，可以等待t秒）。

单步操作总结放在<summary>、</summary>标签内，主要对本次的操作动作进行总结，单步总结操作示例为："点击同意按钮,同意用户协议"、"点击确认按钮,选择当前门店并进入支付页面"、"点击韩元按钮"等。注意：如果是click操作，总结为"点击xxx按钮,yyy"，其中xxx为按钮的名字，yyy为用户操作意图；如果是input操作，总结为点击xxx并输入yyy，其中xxx为输入框名字，yyy为输入内容；如果是scroll操作，总结为"上滑/下滑x个像素"，其中x为像素个数；如果是wait操作，总结为"等待t秒"，其中t为等待的秒数。

注意：1.如果当前页面支持搜索功能，优先使用搜索来实现任务目标。2.如果当前页面是支付二维码页面，则可以考虑任务完成，结束流程。3.如果当前页面处于确认付款阶段，请检查购物车中的商品是否符合任务要求，若不符合要求，请先删除不需要的商品，再继续下一步操作。"""  # pylint: disable=C0301


def __get_python_input() -> PythonInput:
    parser = argparse.ArgumentParser(description="")
    parser.add_argument("--app_entry_mode", default="")
    parser.add_argument("--app_entry_url", default="")
    parser.add_argument("--app_id", default="wx3dcca19d0aa51755")
    parser.add_argument("--app_name", default="星巴克")
    parser.add_argument("--base_url", default="http://drhttpsvr.polaris:8000/v1/llm_luban_user_xiaodezhang_llm_luban_xiaochengxu_qwen25vl32b_v20250704_360_v16_export-0707-19/")  # fmt: skip # pylint: disable=C0301
    parser.add_argument("--from_username", default="")
    parser.add_argument("--headless_mode", default="0")
    parser.add_argument("--instruction", default="帮我点一杯冰美式咖啡")
    parser.add_argument("--model_name", default="llm_luban_user_xiaodezhang_llm_luban_xiaochengxu_qwen25vl32b_v20250704_360_v16_export-0707-19")  # fmt: skip # pylint: disable=C0301
    parser.add_argument("--prompt", default=__DEFAULT_PROMPT)
    parser.add_argument("--prompt_vlt", default=__DEFAULT_PROMPT_VLT)
    parser.add_argument("--prompt_vlt_v1", default=__DEFAULT_PROMPT_VLT_V1)
    parser.add_argument("--rag_base_url", default="http://mmfinderdrannotationsvr.polaris:8080")  # fmt: skip # pylint: disable=C0301
    parser.add_argument("--raw_query", default="帮我点一杯冰美式咖啡")
    parser.add_argument("--run_mode", default="text_infer_v3")
    parser.add_argument("--sandbox_close_applet", default="1")
    parser.add_argument("--sandbox_is_async_action_result", default="0")
    parser.add_argument("--sandbox_skip_launch_applet", default="0")
    parser.add_argument("--special_app_id_list", default="")
    parser.add_argument("--special_str_1", default="")
    parser.add_argument("--special_str_2", default="")
    parser.add_argument("--uin", default="0")
    parser.add_argument("--username", default="")
    parser.add_argument("--vlt_base_url", default="http://drhttpsvr.polaris:8000/v1/llm_luban_xiaochengxu_qwen25vl32b_v20250605_02_checkpoint-650_export-0606-15/")  # fmt: skip # pylint: disable=C0301
    parser.add_argument("--vlt_base_url_v1", default="http://drhttpsvr.polaris:8000/v1/llm-luban-702_didi_Qwen2.5-VL-32B-didi702dest1959_ck140_export-0703-15")  # fmt: skip # pylint: disable=C0301
    parser.add_argument("--vlt_model_name", default="llm_luban_xiaochengxu_qwen25vl32b_v20250605_02_checkpoint-650_export-0606-15")  # fmt: skip # pylint: disable=C0301
    parser.add_argument("--vlt_model_name_v1", default="llm-luban-702_didi_Qwen2.5-VL-32B-didi702dest1959_ck140_export-0703-15")  # fmt: skip # pylint: disable=C0301
    parser.add_argument("--traceparent", default="")
    parser.add_argument("--tracestate", default="")
    args = parser.parse_args()
    res = PythonInput(**args.__dict__)
    if not res.traceparent:
        res.traceparent = context_util.generate_traceparent()
    if not res.tracestate:
        res.tracestate = "appid=mmfinderdrsandbox"
    return res


def __get_standard_start_config(
    python_input: PythonInput,
) -> SandboxActionStandardConfig:
    app_link = f'<a data-miniprogram-isagenttextstyle="1" data-miniprogram-appid="{common_util.base64_encode(python_input.app_id)}" href=" ">{python_input.app_name}</a>'  # pylint: disable=C0301
    action_message_list = [
        SandboxActionMessage(
            style_type=SandboxActionMessageStyleType.TITLE,
            content=(
                f"正在使用{app_link}小程序{python_input.raw_query}"
                if python_input.run_mode != "just_launch_applet"
                else f"正在打开{app_link}小程序"
            ),
        ),
    ]
    return SandboxActionStandardConfig(
        action_message_list_before_action=action_message_list
    )


def __main():
    python_input = __get_python_input()
    python_result = PythonResult(
        answers=[],
        answers_raw_data=[],
        interrupt={},
        log_data=[],
        screens=[],
        standard_output_list=[],
        status=PythonResultStatus.SUCCESS,
        target_id="",
    )

    @context_util.add_trace_span(
        span_name=python_input.run_mode,
        carrier=python_input.model_dump(),
        attributes=python_input.model_dump(),
    )
    def __inner():
        if agent_cls := get_task_agent(python_input.run_mode):
            sandbox_client = SandboxClient(
                base_url=WXMS_BASE_URL,
                app_id=python_input.app_id,
                app_entry_mode=python_input.app_entry_mode,
                app_entry_url=python_input.app_entry_url,
                uin=int(python_input.uin),
                from_username=python_input.from_username,
                username=python_input.username,
                headless_mode=int(python_input.headless_mode),
                standard_start_config=__get_standard_start_config(
                    python_input=python_input,
                ),
                skip_launch_applet=python_input.sandbox_skip_launch_applet == "1",
                is_async_action_result=python_input.sandbox_is_async_action_result
                == "1",
            )
            python_result.target_id = sandbox_client.target_id
            python_result.standard_output_list = sandbox_client.standard_output_list
            if sandbox_client.start_interrupt is not None:
                python_result.interrupt = sandbox_client.start_interrupt
            if sandbox_client.stop_flag:
                return
            # 开始逐步执行操作
            try:
                agent_obj = agent_cls(
                    python_input=python_input,
                    python_result=python_result,
                    sandbox_client=sandbox_client,
                )
                agent_obj.init_step()
                time_util.execute_function_with_timeout(540, agent_obj.run)
            except InterruptError as e:
                python_result.interrupt = e.params  # type: ignore
            except (FunctionTimedOut, Exception) as e:  # pylint: disable=W0718
                python_result.status = PythonResultStatus.FAILED
                logger.error(
                    "execute task failed",
                    extra={
                        "customized_data_info": {
                            "exception": str(e),
                            "traceback": traceback.format_exc(),
                        }
                    },
                )
            finally:
                sandbox_client.standard_end(
                    close_applet=python_input.sandbox_close_applet == "1",
                    config=SandboxActionStandardConfig(
                        action_message_list_before_action=(
                            [
                                SandboxActionMessage(
                                    style_type=SandboxActionMessageStyleType.TITLE,
                                    content="总结操作结果",
                                )
                            ]
                            if python_result.interrupt.get("error_code", 0) != -9
                            and python_input.run_mode != "just_launch_applet"
                            else None
                        )
                    ),
                )
        else:
            raise ValueError(f"wrong run_mode {python_input.run_mode}")

    __inner()
    # 将输出通过 stdout 返回
    sys.stdout.write("\n" + python_result.model_dump_json() + "\n")
    sys.stdout.flush()
    close_logger()
    # 确保后续不会有其他打印输出干扰
    sys.exit(0)


if __name__ == "__main__":
    __main()
