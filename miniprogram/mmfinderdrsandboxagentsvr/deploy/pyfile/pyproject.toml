[tool.poetry]
name = "pyfile"
version = "0.0.1"
description = ""
authors = ["mike<PERSON><PERSON> <<EMAIL>>"]
license = "MIT"
readme = "README.md"

[tool.poetry.dependencies]
jinja2 = "^3.1.6"
lxml = "^5.4.0"
python = "^3.12"
wxms = { version = "^2.84.0", source = "tencent_pypi_wxms" }

[[tool.poetry.source]]
name = "tencent_pypi"
url = "https://mirrors.cloud.tencent.com/pypi/simple/"
priority = "primary"

[[tool.poetry.source]]
name = "tencent_pypi_wxms"
url = "https://yuyiyiwang:<EMAIL>/repository/pypi/tencent_pypi/simple"
priority = "supplemental"

[[tool.poetry.source]]
name = "backup_pypi"
url = "https://pypi.org/simple"
priority = "supplemental"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
